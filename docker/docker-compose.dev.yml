version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: annotation_postgres_dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-annotation}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/scripts/init_extensions.sql:/docker-entrypoint-initdb.d/01_init_extensions.sql
      - ./database/seeds:/docker-entrypoint-initdb.d/seeds
    networks:
      - annotation_dev_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-annotation}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: annotation_redis_dev
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - annotation_dev_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ..
      dockerfile: docker/backend.Dockerfile
      target: development
    container_name: annotation_backend_dev
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-annotation}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - DEBUG=true
      - ENVIRONMENT=development
      - CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
      - backend_dev_uploads:/app/uploads
      - backend_dev_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - annotation_dev_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ../backend
          target: /app
          ignore:
            - __pycache__/
            - "*.pyc"
            - .pytest_cache/

  # 前端应用
  frontend:
    build:
      context: ..
      dockerfile: docker/frontend.Dockerfile
      target: development
    container_name: annotation_frontend_dev
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_APP_ENV=development
    ports:
      - "3000:3000"
    volumes:
      - ../frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - annotation_dev_network
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ../frontend
          target: /app
          ignore:
            - node_modules/
            - dist/
            - .vite/

  # 数据库管理工具 (可选)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: annotation_pgadmin_dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - annotation_dev_network
    restart: unless-stopped
    profiles:
      - tools

  # Redis管理工具 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: annotation_redis_commander_dev
    environment:
      - REDIS_HOSTS=local:redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - annotation_dev_network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  backend_dev_uploads:
    driver: local
  backend_dev_logs:
    driver: local
  pgadmin_dev_data:
    driver: local

networks:
  annotation_dev_network:
    driver: bridge
    name: annotation_dev_network
